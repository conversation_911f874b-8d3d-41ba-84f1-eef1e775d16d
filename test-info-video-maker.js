/**
 * Test Script for Info-Video Maker Functionality
 * 
 * This script tests the info-video maker functionality in the 360° viewer.
 * Run this in the browser console while on the 360° viewer page.
 */

console.log('🧪 Starting Info-Video Maker Tests...');

// Test 1: Check if info-video maker object exists
function testInfoVideoMakerExists() {
  console.log('\n📋 Test 1: Checking if info-video maker object exists...');
  
  // Check localStorage
  const savedData = localStorage.getItem('infoVideoMaker');
  if (savedData) {
    try {
      const parsed = JSON.parse(savedData);
      console.log('✅ Info-video maker data found in localStorage:', parsed);
      return true;
    } catch (error) {
      console.log('❌ Invalid data in localStorage:', error);
      return false;
    }
  } else {
    console.log('ℹ️ No info-video maker data in localStorage (this is normal for first run)');
    return true; // This is okay for first run
  }
}

// Test 2: Test API endpoint
async function testAPIEndpoint() {
  console.log('\n📋 Test 2: Testing API endpoint...');
  
  try {
    // Test GET request
    const response = await fetch('/api/info-video-maker');
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ API endpoint is working. Records found:', data.count);
      console.log('📊 Data:', data.data);
      return true;
    } else {
      console.log('❌ API returned error:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ API request failed:', error);
    return false;
  }
}

// Test 3: Test video selection simulation
function testVideoSelection() {
  console.log('\n📋 Test 3: Simulating video selection...');
  
  // Create test data
  const testVideoData = {
    _360Name: 'Test_360_Image',
    selectedVideoTitle: 'Test Video Title',
    selectedVideoId: 'test-video-id-123',
    lastUpdated: new Date().toISOString()
  };
  
  // Save to localStorage
  localStorage.setItem('infoVideoMaker', JSON.stringify(testVideoData));
  console.log('✅ Test video selection data saved to localStorage');
  
  // Verify it was saved
  const saved = localStorage.getItem('infoVideoMaker');
  const parsed = JSON.parse(saved);
  
  if (parsed.selectedVideoTitle === testVideoData.selectedVideoTitle) {
    console.log('✅ Video selection data verified in localStorage');
    return true;
  } else {
    console.log('❌ Video selection data verification failed');
    return false;
  }
}

// Test 4: Test API save functionality
async function testAPISave() {
  console.log('\n📋 Test 4: Testing API save functionality...');
  
  const testData = {
    _360Name: 'Test_360_API_Save',
    selectedVideoTitle: 'API Test Video',
    selectedVideoId: 'api-test-video-123',
    history: [
      {
        id: Date.now(),
        _360Name: 'Test_360_API_Save',
        videoTitle: 'API Test Video',
        videoId: 'api-test-video-123',
        timestamp: new Date().toISOString(),
        action: 'video_selected'
      }
    ]
  };
  
  try {
    const response = await fetch('/api/info-video-maker', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ API save successful:', result.message);
      console.log('📊 Saved data:', result.data);
      return true;
    } else {
      console.log('❌ API save failed:', result.error);
      return false;
    }
  } catch (error) {
    console.log('❌ API save request failed:', error);
    return false;
  }
}

// Test 5: Test export functionality
function testExportFunctionality() {
  console.log('\n📋 Test 5: Testing export functionality...');
  
  const testData = {
    _360Name: 'Export_Test_360',
    selectedVideoTitle: 'Export Test Video',
    selectedVideoId: 'export-test-123',
    lastUpdated: new Date().toISOString(),
    exportedAt: new Date().toISOString(),
    version: '1.0'
  };
  
  try {
    // Create blob and test
    const blob = new Blob([JSON.stringify(testData, null, 2)], { type: 'application/json' });
    
    if (blob.size > 0) {
      console.log('✅ Export blob created successfully, size:', blob.size, 'bytes');
      console.log('📄 Export data preview:', JSON.stringify(testData, null, 2).substring(0, 100) + '...');
      return true;
    } else {
      console.log('❌ Export blob creation failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Export functionality test failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running all Info-Video Maker tests...\n');
  
  const results = {
    infoVideoMakerExists: testInfoVideoMakerExists(),
    apiEndpoint: await testAPIEndpoint(),
    videoSelection: testVideoSelection(),
    apiSave: await testAPISave(),
    exportFunctionality: testExportFunctionality()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  let passedTests = 0;
  const totalTests = Object.keys(results).length;
  
  for (const [testName, result] of Object.entries(results)) {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${testName}: ${status}`);
    if (result) passedTests++;
  }
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Info-Video Maker functionality is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
  
  return results;
}

// Instructions for manual testing
function showManualTestInstructions() {
  console.log('\n📖 Manual Testing Instructions:');
  console.log('================================');
  console.log('1. Open the 360° viewer at: https://localhost:3003/admin/360s-manager/360-viewer');
  console.log('2. Open the Marker Input Panel on the right side');
  console.log('3. Create a new marker with type "infoVideo"');
  console.log('4. Select a video from the dropdown');
  console.log('5. Check the debug panel on the left for updated info-video maker data');
  console.log('6. Look for the green notification toast at the top');
  console.log('7. Check browser console for log messages');
  console.log('8. Try the Export/Import buttons in the debug panel');
  console.log('9. Switch between different 360° images and repeat');
  console.log('\n🔍 What to look for:');
  console.log('- Green notification when video is selected');
  console.log('- Updated _360Name property in debug panel');
  console.log('- Console logs with "🎥 Info-video maker updated"');
  console.log('- Statistics showing selection counts');
  console.log('- History showing recent selections');
}

// Export functions for manual use
window.testInfoVideoMaker = {
  runAllTests,
  testInfoVideoMakerExists,
  testAPIEndpoint,
  testVideoSelection,
  testAPISave,
  testExportFunctionality,
  showManualTestInstructions
};

console.log('\n🛠️ Test functions available:');
console.log('- testInfoVideoMaker.runAllTests() - Run all automated tests');
console.log('- testInfoVideoMaker.showManualTestInstructions() - Show manual testing guide');
console.log('- Individual test functions also available');

// Auto-run tests if this script is executed directly
if (typeof window !== 'undefined') {
  console.log('\n⏳ Auto-running tests in 2 seconds...');
  setTimeout(() => {
    runAllTests().then(() => {
      showManualTestInstructions();
    });
  }, 2000);
}
