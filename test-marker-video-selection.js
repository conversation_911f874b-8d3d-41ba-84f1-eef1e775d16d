/**
 * Test Script for Marker-Based Video Selection
 * 
 * This script tests that video selection is properly registered in the marker's infoVideo object.
 * Run this in the browser console while on the 360° viewer page.
 */

console.log('🧪 Testing Marker-Based Video Selection...');

// Function to test if marker has proper infoVideo structure
function testMarkerStructure() {
  console.log('\n📋 Test 1: Checking marker structure...');
  
  // Try to access the React component state (this is a hack for testing)
  const reactFiberKey = Object.keys(document.querySelector('[data-testid="360-viewer"]') || {}).find(key => key.startsWith('__reactFiber'));
  
  if (!reactFiberKey) {
    console.log('❌ Could not access React component state');
    return false;
  }
  
  console.log('✅ React component accessible for testing');
  return true;
}

// Function to simulate video selection and check result
function simulateVideoSelection() {
  console.log('\n📋 Test 2: Simulating video selection...');
  
  // Look for infoVideo markers in the UI
  const markerElements = document.querySelectorAll('[data-marker-type="infoVideo"]');
  
  if (markerElements.length === 0) {
    console.log('ℹ️ No infoVideo markers found in UI. Create one first.');
    return false;
  }
  
  console.log(`✅ Found ${markerElements.length} infoVideo marker(s) in UI`);
  
  // Look for video selection dropdowns
  const videoDropdowns = document.querySelectorAll('select[data-marker-field="video"]');
  
  if (videoDropdowns.length === 0) {
    console.log('ℹ️ No video selection dropdowns found. Markers may not be loaded yet.');
    return false;
  }
  
  console.log(`✅ Found ${videoDropdowns.length} video selection dropdown(s)`);
  return true;
}

// Function to check if video info is displayed
function checkVideoInfoDisplay() {
  console.log('\n📋 Test 3: Checking video info display...');
  
  // Look for video info displays
  const videoInfoElements = document.querySelectorAll('[data-video-info]');
  
  if (videoInfoElements.length === 0) {
    console.log('ℹ️ No video info displays found. Select a video first.');
    return false;
  }
  
  console.log(`✅ Found ${videoInfoElements.length} video info display(s)`);
  
  // Check if they contain video names
  let hasVideoNames = false;
  videoInfoElements.forEach((element, index) => {
    const videoName = element.textContent;
    if (videoName && videoName.trim() !== '' && !videoName.includes('none selected')) {
      console.log(`✅ Video info ${index + 1}: "${videoName}"`);
      hasVideoNames = true;
    }
  });
  
  if (!hasVideoNames) {
    console.log('ℹ️ No video names found in displays. Select a video first.');
    return false;
  }
  
  return true;
}

// Function to check console logs for video selection
function checkConsoleLogs() {
  console.log('\n📋 Test 4: Monitoring console logs...');
  
  // Store original console.log
  const originalLog = console.log;
  let videoSelectionDetected = false;
  
  // Override console.log to detect video selection logs
  console.log = function(...args) {
    const message = args.join(' ');
    
    if (message.includes('🎥 Video selected for marker:')) {
      videoSelectionDetected = true;
      console.log('✅ Video selection detected in console logs!');
    }
    
    if (message.includes('📊 Updated infoVideo object:')) {
      console.log('✅ InfoVideo object update detected in console logs!');
    }
    
    // Call original console.log
    originalLog.apply(console, args);
  };
  
  // Restore original console.log after 10 seconds
  setTimeout(() => {
    console.log = originalLog;
    if (videoSelectionDetected) {
      console.log('✅ Console log monitoring completed - video selection detected');
    } else {
      console.log('ℹ️ Console log monitoring completed - no video selection detected yet');
    }
  }, 10000);
  
  console.log('🔍 Console log monitoring active for 10 seconds...');
  return true;
}

// Function to check API calls
function checkAPIUpdates() {
  console.log('\n📋 Test 5: Monitoring API calls...');
  
  // Store original fetch
  const originalFetch = window.fetch;
  let apiUpdateDetected = false;
  
  // Override fetch to detect API calls
  window.fetch = function(...args) {
    const url = args[0];
    const options = args[1];
    
    if (url && url.includes('/api/360s/') && options && options.method === 'PATCH') {
      console.log('✅ 360° API update detected:', url);
      apiUpdateDetected = true;
      
      // Try to log the request body
      if (options.body) {
        try {
          const body = JSON.parse(options.body);
          if (body.markerList) {
            console.log('📊 Marker list in API call:', body.markerList.length, 'markers');
            
            // Check for infoVideo objects
            const infoVideoMarkers = body.markerList.filter(marker => 
              marker.markerType === 'infoVideo' && marker.infoVideo
            );
            
            if (infoVideoMarkers.length > 0) {
              console.log('✅ InfoVideo markers with infoVideo objects found in API call:');
              infoVideoMarkers.forEach((marker, index) => {
                console.log(`  ${index + 1}. ${marker.name}:`, {
                  videoName: marker.infoVideo.videoName,
                  videoId: marker.infoVideo.videoId,
                  _360Name: marker.infoVideo._360Name
                });
              });
            }
          }
        } catch (error) {
          console.log('ℹ️ Could not parse API request body');
        }
      }
    }
    
    // Call original fetch
    return originalFetch.apply(this, args);
  };
  
  // Restore original fetch after 30 seconds
  setTimeout(() => {
    window.fetch = originalFetch;
    if (apiUpdateDetected) {
      console.log('✅ API monitoring completed - updates detected');
    } else {
      console.log('ℹ️ API monitoring completed - no updates detected yet');
    }
  }, 30000);
  
  console.log('🔍 API call monitoring active for 30 seconds...');
  return true;
}

// Main test function
async function runMarkerVideoSelectionTests() {
  console.log('🚀 Running Marker-Based Video Selection Tests...\n');
  
  const results = {
    markerStructure: testMarkerStructure(),
    videoSelection: simulateVideoSelection(),
    videoInfoDisplay: checkVideoInfoDisplay(),
    consoleLogs: checkConsoleLogs(),
    apiUpdates: checkAPIUpdates()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  let passedTests = 0;
  const totalTests = Object.keys(results).length;
  
  for (const [testName, result] of Object.entries(results)) {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${testName}: ${status}`);
    if (result) passedTests++;
  }
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  return results;
}

// Instructions for manual testing
function showManualTestInstructions() {
  console.log('\n📖 Manual Testing Instructions:');
  console.log('================================');
  console.log('1. Create an infoVideo marker:');
  console.log('   - Open Marker Input Panel (right side)');
  console.log('   - Enter marker name');
  console.log('   - Select "infoVideo" type');
  console.log('   - Click "Add Marker"');
  console.log('');
  console.log('2. Select a video:');
  console.log('   - Find your infoVideo marker in the list');
  console.log('   - Use the dropdown to select a video');
  console.log('   - Watch for green notification toast');
  console.log('');
  console.log('3. Verify the results:');
  console.log('   - Check for green video info box below dropdown');
  console.log('   - Look at debug panel (left side) for marker info');
  console.log('   - Check browser console for log messages');
  console.log('   - Verify API calls in Network tab');
  console.log('');
  console.log('🔍 What to look for:');
  console.log('- Green notification: "Video [title] selected for marker"');
  console.log('- Green info box showing selected video and 360° name');
  console.log('- Console logs: "🎥 Video selected for marker"');
  console.log('- Console logs: "📊 Updated infoVideo object"');
  console.log('- Debug panel showing infoVideo markers with video names');
  console.log('- API PATCH calls to /api/360s/[id] with marker data');
}

// Export functions for manual use
window.testMarkerVideoSelection = {
  runMarkerVideoSelectionTests,
  testMarkerStructure,
  simulateVideoSelection,
  checkVideoInfoDisplay,
  checkConsoleLogs,
  checkAPIUpdates,
  showManualTestInstructions
};

console.log('\n🛠️ Test functions available:');
console.log('- testMarkerVideoSelection.runMarkerVideoSelectionTests() - Run all tests');
console.log('- testMarkerVideoSelection.showManualTestInstructions() - Show manual testing guide');
console.log('- Individual test functions also available');

// Auto-run tests if this script is executed directly
if (typeof window !== 'undefined') {
  console.log('\n⏳ Auto-running tests in 3 seconds...');
  setTimeout(() => {
    runMarkerVideoSelectionTests().then(() => {
      setTimeout(() => {
        showManualTestInstructions();
      }, 1000);
    });
  }, 3000);
}
