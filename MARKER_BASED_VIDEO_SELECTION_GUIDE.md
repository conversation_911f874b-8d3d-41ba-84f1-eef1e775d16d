# Marker-Based Video Selection Implementation Guide

## Overview

The video selection functionality has been integrated directly into the marker system. When users select a video for an `infoVideo` marker type, the video information is now stored within the marker's data structure rather than in a separate global state.

## Key Changes Made

### 1. **Updated Marker Schema** (`src/models/_360Model.js`)

Added an `infoVideo` object to the marker schema:

```javascript
const MarkerSchema = new Schema({
    name: { type: String, required: true },
    markerType: { /* ... */ },
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 },
    _360Name: { type: String, default: '' },
    id: { type: String, default: '' },
    // NEW: infoVideo object for storing video selection data
    infoVideo: {
        videoName: { type: String, default: '' },      // Video title
        videoId: { type: String, default: '' },        // Video ID
        _360Name: { type: String, default: '' },        // 360° image name
        lastUpdated: { type: Date, default: null }
    }
}, { _id: false });
```

### 2. **Enhanced Marker Creation** (`src/components/360s/MarkersInputList.jsx`)

When creating `infoVideo` markers, the system now automatically adds the `infoVideo` object:

```javascript
// Create new marker with default values
const newMarker = {
    name: input.name.trim(),
    markerType: input.markerType,
    x: 0, y: 0, z: 0,
    // Add infoVideo object for infoVideo markers
    ...(input.markerType === 'infoVideo' && {
        infoVideo: {
            videoName: '',
            videoId: '',
            lastUpdated: null
        }
    })
}
```

### 3. **Video Selection Handler** ⭐ KEY FIX

Updated the `handleUpdateMarker` function to store video information within the marker, including the current 360° image name:

```javascript
// Check if this is a video selection for an infoVideo marker
if (marker?.markerType === 'infoVideo' && updates.id) {
    const selectedVideo = apiData.videoGallery.find(video => video._id === updates.id);
    if (selectedVideo) {
        // Update the infoVideo object within the marker, including current 360° name
        finalUpdates.infoVideo = {
            videoName: selectedVideo.title,              // ✅ Video title from API
            videoId: selectedVideo._id,                  // ✅ Video ID
            _360Name: _360Object?.name || '',            // ✅ Current 360° image name
            lastUpdated: new Date()                      // ✅ Timestamp
        };

        console.log('🎥 Video selected for marker:', markerName, 'Video:', selectedVideo.title);
        console.log('📊 Current 360° Name:', _360Object?.name);
        console.log('📊 Updated infoVideo object:', finalUpdates.infoVideo);
    }
}
```

**This is the key fix that ensures:**
- Video title is stored in `infoVideo.videoName`
- Current 360° image name is stored in `infoVideo._360Name`
- Both are saved together when the marker is persisted

### 4. **Visual Feedback**

Added visual indicators in the marker list to show selected video information:

```javascript
{/* Show selected video info for infoVideo markers */}
{marker?.markerType === 'infoVideo' && marker?.infoVideo?.videoName && (
    <div className="w-full mt-1 p-2 bg-green-50 border border-green-200 rounded text-xs">
        <div className="flex items-center gap-1">
            <span className="text-green-600 font-semibold">🎥 Selected Video:</span>
        </div>
        <div className="text-green-700 font-medium truncate mt-1">
            {marker.infoVideo.videoName}
        </div>
        <div className="text-green-600 text-xs mt-1">
            Updated: {marker.infoVideo.lastUpdated ? new Date(marker.infoVideo.lastUpdated).toLocaleString() : 'Never'}
        </div>
    </div>
)}
```

### 5. **Debug Panel Updates**

Modified the debug panel to show marker-based video information:

```javascript
<div className="font-semibold text-green-400">📹 InfoVideo Markers:</div>
{(() => {
    const infoVideoMarkers = _360Object?.markerList?.filter(marker => marker.markerType === 'infoVideo') || [];
    if (infoVideoMarkers.length === 0) {
        return <div className="text-gray-400">No infoVideo markers</div>;
    }
    return infoVideoMarkers.map((marker, index) => (
        <div key={index} className="mt-1 p-1 bg-black/40 rounded">
            <div className="text-green-300 font-medium">{marker.name}</div>
            <div className={marker.infoVideo?.videoName ? 'text-green-300' : 'text-gray-400'}>
                Video: {marker.infoVideo?.videoName || 'none selected'}
            </div>
            {marker.infoVideo?.lastUpdated && (
                <div className="text-blue-300 text-xs">
                    Updated: {new Date(marker.infoVideo.lastUpdated).toLocaleTimeString()}
                </div>
            )}
        </div>
    ));
})()}
```

## Data Structure

### Before (Global State)
```javascript
// Separate global state
const [infoVideoMaker, setInfoVideoMaker] = useState({
    _360Name: '',
    selectedVideoTitle: '',
    selectedVideoId: '',
    lastUpdated: null,
});
```

### After (Marker-Based)
```javascript
// Within each infoVideo marker
{
    name: "Video Marker 1",
    markerType: "infoVideo",
    x: 0, y: 0, z: 0,
    id: "video-gallery-id-123",
    infoVideo: {
        videoName: "Selected Video Title",        // ✅ Video title stored here
        videoId: "video-gallery-id-123",
        _360Name: "Current_360_Image_Name",       // ✅ 360° image name stored here
        lastUpdated: "2025-01-23T10:30:00.000Z"
    }
}
```

## Benefits of the New Approach

1. **Data Integrity**: Video selection data is now part of the marker's persistent data structure
2. **Scalability**: Each marker can have its own video selection without conflicts
3. **Persistence**: Video selections are automatically saved with marker data
4. **Consistency**: Follows the existing pattern of storing marker-specific data within markers
5. **Migration Support**: Existing markers are automatically migrated to include the `infoVideo` object

## Testing Instructions

### 1. **Create an InfoVideo Marker**
1. Open the 360° viewer at: `https://localhost:3003/admin/360s-manager/360-viewer`
2. Open the Marker Input Panel on the right side
3. Enter a marker name and select "infoVideo" as the marker type
4. Click "Add Marker"

### 2. **Select a Video**
1. In the marker list, find your newly created infoVideo marker
2. Use the dropdown to select a video from the video gallery
3. Observe the green notification toast
4. Check that the selected video information appears below the dropdown

### 3. **Verify Persistence**
1. Check the debug panel (development mode) to see the marker's infoVideo object
2. Switch to a different 360° image and back to verify the selection persists
3. Refresh the page to ensure the data is saved to the database

### 4. **Check Database**
1. The marker data with the infoVideo object should be saved in the MongoDB collection
2. Use the API endpoint `/api/360s` to verify the marker structure includes the infoVideo object

## Migration Notes

- Existing `infoVideo` markers without the `infoVideo` object will be automatically migrated
- The migration adds an empty `infoVideo` object with default values
- No data loss occurs during the migration process

## API Integration

The marker data, including the `infoVideo` object, is automatically saved through the existing 360° API endpoints:
- `PATCH /api/360s/[id]` - Updates marker data including infoVideo objects
- `GET /api/360s` - Retrieves marker data with infoVideo objects

## Troubleshooting

### Video Selection Not Showing
- Ensure the marker type is "infoVideo"
- Check that videos exist in the video gallery
- Verify the API endpoints are responding correctly

### Data Not Persisting
- Check browser console for API errors
- Verify MongoDB connection
- Ensure the marker schema includes the infoVideo object

### Migration Issues
- Check the `migrateMarkerData` function in `MarkersInputList.jsx`
- Verify existing markers are being processed correctly
- Look for console warnings about migration failures

## Future Enhancements

1. **Bulk Video Assignment**: Allow assigning the same video to multiple markers
2. **Video Preview**: Show video thumbnails in the marker list
3. **Video Validation**: Ensure selected videos still exist before displaying
4. **Export/Import**: Include infoVideo data in marker export/import functionality
