import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import mongoose from 'mongoose';

// Define schema for info-video maker data
const InfoVideoMakerSchema = new mongoose.Schema({
  _360Name: { type: String, required: true },
  selectedVideoTitle: { type: String, required: true },
  selectedVideoId: { type: String, required: true },
  lastUpdated: { type: Date, default: Date.now },
  history: [{
    id: Number,
    _360Name: String,
    videoTitle: String,
    videoId: String,
    timestamp: Date,
    action: String
  }]
}, { timestamps: true });

// Create or get the model
const InfoVideoMaker = mongoose.models.InfoVideoMaker || mongoose.model('InfoVideoMaker', InfoVideoMakerSchema);

// GET /api/info-video-maker - Get all info-video maker records
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const _360Name = searchParams.get('_360Name');
    const limit = parseInt(searchParams.get('limit')) || 50;
    
    // Build query
    const query = {};
    if (_360Name) {
      query._360Name = _360Name;
    }
    
    const records = await InfoVideoMaker.find(query)
      .sort({ lastUpdated: -1 })
      .limit(limit)
      .lean();
    
    return NextResponse.json({
      success: true,
      data: records,
      count: records.length
    });
    
  } catch (error) {
    console.error('Error fetching info-video maker data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to fetch info-video maker data',
      },
      { status: 500 }
    );
  }
}

// POST /api/info-video-maker - Create or update info-video maker record
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['_360Name', 'selectedVideoTitle', 'selectedVideoId'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Check if record already exists for this 360 name
    const existingRecord = await InfoVideoMaker.findOne({ _360Name: body._360Name });
    
    if (existingRecord) {
      // Update existing record
      const updatedRecord = await InfoVideoMaker.findByIdAndUpdate(
        existingRecord._id,
        {
          selectedVideoTitle: body.selectedVideoTitle,
          selectedVideoId: body.selectedVideoId,
          lastUpdated: new Date(),
          history: body.history || []
        },
        { new: true }
      );
      
      return NextResponse.json({
        success: true,
        data: updatedRecord,
        message: 'Info-video maker data updated successfully'
      });
    } else {
      // Create new record
      const newRecord = new InfoVideoMaker({
        _360Name: body._360Name,
        selectedVideoTitle: body.selectedVideoTitle,
        selectedVideoId: body.selectedVideoId,
        lastUpdated: new Date(),
        history: body.history || []
      });
      
      const savedRecord = await newRecord.save();
      
      return NextResponse.json({
        success: true,
        data: savedRecord,
        message: 'Info-video maker data created successfully'
      }, { status: 201 });
    }
    
  } catch (error) {
    console.error('Error saving info-video maker data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to save info-video maker data',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/info-video-maker - Delete info-video maker records
export async function DELETE(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const _360Name = searchParams.get('_360Name');
    const id = searchParams.get('id');
    
    if (id) {
      // Delete specific record by ID
      const deletedRecord = await InfoVideoMaker.findByIdAndDelete(id);
      
      if (!deletedRecord) {
        return NextResponse.json(
          {
            success: false,
            error: 'Not Found',
            message: 'Info-video maker record not found',
          },
          { status: 404 }
        );
      }
      
      return NextResponse.json({
        success: true,
        message: 'Info-video maker record deleted successfully'
      });
    } else if (_360Name) {
      // Delete all records for specific 360 name
      const result = await InfoVideoMaker.deleteMany({ _360Name });
      
      return NextResponse.json({
        success: true,
        message: `${result.deletedCount} info-video maker records deleted successfully`
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Bad Request',
          message: 'Either id or _360Name parameter is required',
        },
        { status: 400 }
      );
    }
    
  } catch (error) {
    console.error('Error deleting info-video maker data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal Server Error',
        message: 'Failed to delete info-video maker data',
      },
      { status: 500 }
    );
  }
}
