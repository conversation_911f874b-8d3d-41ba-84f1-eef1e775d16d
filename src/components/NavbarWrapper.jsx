'use client'
import { useSearchParams } from 'next/navigation'
import React from 'react'
import NavbarLandingPage from './NavbarLandingPage'
import _360Navbar from './360s/_360Navbar'

export default function NavbarWrapper({condition}) {
    const searchParams=useSearchParams()
    const id=searchParams.get('id')
    // console.log('NavbarWrapper',id)
  return (
     <div className='flex absolute w-full left-0 top-0 z-10'>
      <div className='flex w-full h-fit items-center justify-between'>
      {id=='New_entrance_360_002' && !condition ? <NavbarLandingPage/> : <_360Navbar/>}
      {/* {condition && <ß/>} */}
      </div>
    </div>
    
  )
}
