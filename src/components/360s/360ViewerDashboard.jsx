'use client';

import { useState, useEffect, useRef, Suspense, useMemo, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { MdArrowBack, MdHelp, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphereDashbard';
import ThumbnailPanel from './ThumbnailPanel';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';
import TextureStatusIndicator from './TextureStatusIndicator';
import MarkerInputPanel from './MarkerInputPanel';
import _360InfoMarkers from './_360InfoMarkersDashboard';
import { useContextExperience } from '@/contexts/useContextExperience';
// import { sortedArray } from 'three/src/animation/AnimationUtils'; // This import seems unused and might cause issues if not a valid module

export default function ThreeSixtyViewer() {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  // Initialize _360Object with default structure to avoid undefined errors
  const [_360Object, set_360Object] = useState({
    cameraPosition: 0,
    _360Rotation: 0,
    markerList: [],
  });

  // Initialize info-video maker object to store selected video information
  const [infoVideoMaker, setInfoVideoMaker] = useState(() => {
    // Try to load from localStorage on initialization
    if (typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('infoVideoMaker');
        if (saved) {
          return JSON.parse(saved);
        }
      } catch (error) {
        console.warn('Failed to load info-video maker from localStorage:', error);
      }
    }
    return {
      _360Name: '',
      selectedVideoTitle: '',
      selectedVideoId: '',
      lastUpdated: null,
    };
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [resetView, setResetView] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);

  const {experienceState,disptachExperience}=useContextExperience()

  const [showHelp, setShowHelp] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);

  // Notification state for video selection
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });

  // History tracking for video selections
  const [videoSelectionHistory, setVideoSelectionHistory] = useState([]);

  // Derive currentImage from threeSixties array
  const currentImage = useMemo(() => {
    const image = threeSixties[currentImageIndex];
    return image;
  }, [threeSixties, currentImageIndex]);

  // Calculate statistics for video selections
  const videoSelectionStats = useMemo(() => {
    const unique360s = new Set(videoSelectionHistory.map(entry => entry._360Name));
    const uniqueVideos = new Set(videoSelectionHistory.map(entry => entry.videoTitle));

    return {
      totalSelections: videoSelectionHistory.length,
      unique360Images: unique360s.size,
      uniqueVideos: uniqueVideos.size,
      currentSessionSelections: videoSelectionHistory.filter(entry => {
        const entryDate = new Date(entry.timestamp);
        const now = new Date();
        return entryDate.toDateString() === now.toDateString();
      }).length
    };
  }, [videoSelectionHistory]);

  // console.log('ThreeSixtyViewer:',experienceState)


  // Function to handle video selection notifications (simplified for marker-based approach)
  const handleVideoSelectionNotification = (videoTitle, videoId = '') => {
    // Show notification and add to history when video is selected
    if (videoTitle) {
      console.log(`✅ Video "${videoTitle}" selected for marker in 360° image "${currentImage?.name}"`);

      // Add to history
      const historyEntry = {
        id: Date.now(),
        _360Name: currentImage?.name || '',
        videoTitle,
        videoId,
        timestamp: new Date().toISOString(),
        action: 'video_selected'
      };

      setVideoSelectionHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10 entries

      setNotification({
        show: true,
        message: `Video "${videoTitle}" selected for marker`,
        type: 'success'
      });

      // Auto-hide notification after 3 seconds
      setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
    }
  };

  // Function to export info-video maker data
  const exportInfoVideoMakerData = () => {
    const dataToExport = {
      ...infoVideoMaker,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `info-video-maker-${currentImage?.name || 'data'}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setNotification({
      show: true,
      message: 'Info-video maker data exported successfully',
      type: 'success'
    });

    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  // Function to import info-video maker data
  const importInfoVideoMakerData = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target.result);

        // Validate the imported data structure
        if (importedData && typeof importedData === 'object') {
          setInfoVideoMaker({
            _360Name: importedData._360Name || '',
            selectedVideoTitle: importedData.selectedVideoTitle || '',
            selectedVideoId: importedData.selectedVideoId || '',
            lastUpdated: new Date().toISOString(),
          });

          setNotification({
            show: true,
            message: 'Info-video maker data imported successfully',
            type: 'success'
          });

          setTimeout(() => {
            setNotification(prev => ({ ...prev, show: false }));
          }, 3000);
        } else {
          throw new Error('Invalid data format');
        }
      } catch (error) {
        console.error('Failed to import info-video maker data:', error);
        setNotification({
          show: true,
          message: 'Failed to import data. Please check the file format.',
          type: 'error'
        });

        setTimeout(() => {
          setNotification(prev => ({ ...prev, show: false }));
        }, 3000);
      }
    };

    reader.readAsText(file);
    // Reset the input value so the same file can be selected again
    event.target.value = '';
  };

  // Function to save info-video maker data to API
  const saveInfoVideoMakerToAPI = async () => {
    if (!infoVideoMaker.selectedVideoTitle || !infoVideoMaker._360Name) {
      setNotification({
        show: true,
        message: 'No video selection data to save',
        type: 'error'
      });
      setTimeout(() => setNotification(prev => ({ ...prev, show: false })), 3000);
      return;
    }

    try {
      const response = await fetch('/api/info-video-maker', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...infoVideoMaker,
          history: videoSelectionHistory.slice(0, 5), // Include recent history
        }),
      });

      const result = await response.json();

      if (result.success) {
        setNotification({
          show: true,
          message: 'Info-video maker data saved to server',
          type: 'success'
        });
      } else {
        throw new Error(result.error || 'Failed to save data');
      }
    } catch (error) {
      console.error('Failed to save info-video maker data:', error);
      setNotification({
        show: true,
        message: 'Failed to save data to server',
        type: 'error'
      });
    }

    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 3000);
  };

  // Effect to update _360Object when currentImage changes with proper marker synchronization
  useEffect(() => {
    if (!currentImage) {
      // Clear state when no current image to prevent stale data
      set_360Object({
        _id: '',
        name: '',
        cameraPosition: 0,
        _360Rotation: 0,
        markerList: [],
      });

      // Clear info-video maker when no current image
      setInfoVideoMaker(prev => ({
        ...prev,
        _360Name: '',
        selectedVideoTitle: '',
        selectedVideoId: '',
        lastUpdated: null,
      }));
      return;
    }

    const new_360ObjectState = {
      _id: currentImage._id,
      name: currentImage.name, // Include name for proper state management
      cameraPosition: currentImage.cameraPosition || 0,
      _360Rotation: currentImage._360Rotation || 0,
      markerList: Array.isArray(currentImage.markerList) ? [...currentImage.markerList] : [], // Deep copy to prevent reference issues
    };

    // Always update state when currentImage changes to ensure marker synchronization
    set_360Object(new_360ObjectState);

    // Update info-video maker with current 360 name
    setInfoVideoMaker(prev => ({
      ...prev,
      _360Name: currentImage.name || '',
      lastUpdated: new Date().toISOString(),
    }));

    let timer;
    setResetView(true);
    timer = setTimeout(() => setResetView(false), 150); // Slightly longer delay for smoother transition

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentImage]);


  // Save info-video maker data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined' && infoVideoMaker.lastUpdated) {
      try {
        localStorage.setItem('infoVideoMaker', JSON.stringify(infoVideoMaker));
      } catch (error) {
        console.warn('Failed to save info-video maker to localStorage:', error);
      }
    }
  }, [infoVideoMaker]);

  // Fetch 360° images from API
  useEffect(() => {
    fetchThreeSixties();
  }, []);

  // Add global refresh function for child components to trigger data refresh
  useEffect(() => {
    window.refreshDashboardData = () => {
      fetchThreeSixties(true); // Preserve current index when refreshing
    };

    // Suppress WebXR emulator extension warnings
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args.join(' ');
      if (message.includes('WebXR emulator extension') ||
          message.includes('overrides native WebXR API')) {
        return; // Suppress WebXR warnings
      }
      originalConsoleWarn.apply(console, args);
    };

    // Cleanup on unmount
    return () => {
      delete window.refreshDashboardData;
      console.warn = originalConsoleWarn; // Restore original console.warn
    };
  }, []);

  // --- Image change handler for dashboard ---
  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;

    setIsTransitioning(true);

    // Clear current marker state to prevent stale data during transition
    set_360Object(prev => ({
      ...prev,
      markerList: [] // Clear markers during transition
    }));

    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 300));

    setCurrentImageIndex(index);

    // Log the selected 360 image name
    const selectedImage = threeSixties[index];
    if (selectedImage) {
      console.log('360 Image selected:', selectedImage.name);
    }

    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 300));

    setIsTransitioning(false);
  };



  const fetchThreeSixties = async (preserveCurrentIndex = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/360s?sort=priority&order=asc&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        const sortedData = data.data.sort((b, a) => (a.priority || 0) - (b.priority || 0));
        setThreeSixties(sortedData);

        // Preserve current image index if refreshing, otherwise start from 0
        if (!preserveCurrentIndex) {
          setCurrentImageIndex(0);
        } else {
          // Ensure current index is still valid after refresh
          setCurrentImageIndex(prev => Math.min(prev, sortedData.length - 1));
        }

        initializeTextureLoading(sortedData);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    const sortedImages = [...images].sort((b, a) => (a.priority || 0) - (b.priority || 0));

    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index,
      status: 'pending'
    })));
  };

  const handleBack = () => {
    router.push('/admin/360s-manager/file-manager');
  };

  // Enhanced URL resolution for production compatibility
  const resolveImageUrl = useCallback((url) => {
    if (!url) return null;

    // If it's already a full URL (Firebase, CDN), return as-is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For local files, use our serving API in production
    if (process.env.NODE_ENV === 'production') {
      // Extract filename from path
      const filename = url.split('/').pop();
      return `/api/360s/serve/${filename}`;
    }

    // In development, use direct path
    return url.startsWith('/') ? url : `/${url}`;
  }, []);

  // Memoize the props object specifically for PanoramicSphere
  const panoSphereProps = useMemo(() => {
    const resolvedUrl = currentImage?.url ? resolveImageUrl(currentImage.url) : null;

    return {
      currentImage: currentImage,
      imageUrl: resolvedUrl,
      imageId: currentImage?._id,
      textureCache: textureCache,
      setTextureCache: setTextureCache,
      loadingQueue: loadingQueue,
      setLoadingQueue: setLoadingQueue,
      _360Object: _360Object, // Pass the actual _360Object state
      set_360Object: set_360Object,
      onTextureLoad: () => { /* Texture loaded successfully */ },
      resetView: resetView,
    };
  }, [
    currentImage,
    textureCache,
    setTextureCache,
    loadingQueue,
    setLoadingQueue,
    _360Object, // Dependency for _360Object state
    resetView,
    resolveImageUrl,
  ]);

  // Memoize the props object specifically for _360InfoMarkers with proper dependency tracking
  const infoMarkersProps = useMemo(() => {
    return {
      markerList: _360Object?.markerList || [],
      set_360Object: set_360Object,
      disptachExperience:disptachExperience,
      experienceState:experienceState,
      currentImageId: _360Object?._id, // Add currentImageId to force re-render on image change
    };
  }, [_360Object?.markerList, _360Object?._id, set_360Object]);


  if (isLoading || !currentImage) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* Top Controls */}
      <div className="absolute top-4 left-4 right-4 z-20 flex justify-between items-center">
        <button
          onClick={handleBack}
          className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
        >
          <MdArrowBack size={24} />
        </button>
        
        <div className="text-center text-white">
          <h1 className="text-xl font-bold">{currentImage?.name || 'Untitled'}</h1>
          <p className="text-sm text-gray-300">
            {currentImageIndex + 1} of {threeSixties.length}
          </p>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
          >
            <MdHelp size={24} />
          </button>
        </div>
      </div>

      {/* Navigation Arrows */}
      {threeSixties.length > 1 && (
        <>
          <button
            onClick={() => handleImageChange(currentImageIndex - 1)}
            disabled={currentImageIndex === 0 || isTransitioning}
            className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === 0 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateBefore size={32} />
          </button>

          <button
            onClick={() => handleImageChange(currentImageIndex + 1)}
            disabled={currentImageIndex === threeSixties.length - 1 || isTransitioning}
            className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === threeSixties.length - 1 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateNext size={32} />
          </button>
        </>
      )}

      {/* Thumbnail Panel */}
      <ThumbnailPanel
        images={threeSixties}
        currentIndex={currentImageIndex}
        onImageSelect={handleImageChange}
        isTransitioning={isTransitioning}
      />

      {/* Reset button */}
      <div onClick={()=>setResetView(!resetView)} className='absolute text-xs px-4 flex items-center cursor-pointer hover:bg-black/75 ease-linear left-20 text-white h-12 p-2 top-20 z-30 transition-all duration-300 capitalize bg-black/60 rounded-md'>reset view</div>

      {/* Video Selection Notification */}
      {notification.show && (
        <div className={`absolute top-4 right-4 left-4 mx-auto max-w-md z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
          notification.type === 'success' ? 'bg-green-600/90' : 'bg-red-600/90'
        } text-white backdrop-blur-sm`}>
          <div className="flex items-center space-x-2">
            <span className="text-lg">🎥</span>
            <span className="text-sm font-medium">{notification.message}</span>
          </div>
        </div>
      )}

      {/* Debug Info - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className='absolute top-20 left-4 bg-black/80 text-white p-2 rounded text-xs z-30 max-w-xs'>
          <div>Current Image ID: {currentImage?._id || 'none'}</div>
          <div>360Object ID: {_360Object?._id || 'none'}</div>
          <div>Markers Count: {_360Object?.markerList?.length || 0}</div>
          <div>Marker Names: {_360Object?.markerList?.map(m => m.name).join(', ') || 'none'}</div>
          <div className="mt-2 border-t border-white/20 pt-2">
            <div className="font-semibold text-green-400">📹 InfoVideo Markers:</div>
            {(() => {
              const infoVideoMarkers = _360Object?.markerList?.filter(marker => marker.markerType === 'infoVideo') || [];
              if (infoVideoMarkers.length === 0) {
                return <div className="text-gray-400">No infoVideo markers</div>;
              }
              return infoVideoMarkers.map((marker, index) => (
                <div key={index} className="mt-1 p-1 bg-black/40 rounded">
                  <div className="text-green-300 font-medium">{marker.name}</div>
                  <div className={marker.infoVideo?.videoName ? 'text-green-300' : 'text-gray-400'}>
                    Video: {marker.infoVideo?.videoName || 'none selected'}
                  </div>
                  {marker.infoVideo?.lastUpdated && (
                    <div className="text-blue-300 text-xs">
                      Updated: {new Date(marker.infoVideo.lastUpdated).toLocaleTimeString()}
                    </div>
                  )}
                </div>
              ));
            })()}
            <div className="mt-2 border-t border-white/20 pt-2 flex flex-col space-y-1">
              <button
                onClick={saveInfoVideoMakerToAPI}
                className="bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-xs transition-colors"
                disabled={!infoVideoMaker.selectedVideoTitle}
              >
                💾 Save to Server
              </button>
              <button
                onClick={exportInfoVideoMakerData}
                className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors"
              >
                📤 Export Data
              </button>
              <label className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors cursor-pointer text-center">
                📥 Import Data
                <input
                  type="file"
                  accept=".json"
                  onChange={importInfoVideoMakerData}
                  className="hidden"
                />
              </label>
            </div>
            {videoSelectionHistory.length > 0 && (
              <>
                <div className="mt-2 border-t border-white/20 pt-2">
                  <div className="font-semibold text-cyan-400">📊 Statistics:</div>
                  <div className="grid grid-cols-2 gap-1 mt-1 text-xs">
                    <div className="bg-black/40 p-1 rounded">
                      <div className="text-cyan-300">Total: {videoSelectionStats.totalSelections}</div>
                    </div>
                    <div className="bg-black/40 p-1 rounded">
                      <div className="text-cyan-300">Today: {videoSelectionStats.currentSessionSelections}</div>
                    </div>
                    <div className="bg-black/40 p-1 rounded">
                      <div className="text-cyan-300">360s: {videoSelectionStats.unique360Images}</div>
                    </div>
                    <div className="bg-black/40 p-1 rounded">
                      <div className="text-cyan-300">Videos: {videoSelectionStats.uniqueVideos}</div>
                    </div>
                  </div>
                </div>
                <div className="mt-2 border-t border-white/20 pt-2">
                  <div className="font-semibold text-yellow-400">📜 Recent History:</div>
                  <div className="max-h-32 overflow-y-auto space-y-1 mt-1">
                    {videoSelectionHistory.slice(0, 5).map((entry) => (
                      <div key={entry.id} className="text-xs bg-black/40 p-1 rounded">
                        <div className="text-yellow-300 font-medium truncate">{entry.videoTitle}</div>
                        <div className="text-gray-400">for {entry._360Name}</div>
                        <div className="text-gray-500">{new Date(entry.timestamp).toLocaleTimeString()}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Marker Input Panel */}
      <MarkerInputPanel
        _360Object={_360Object}
        _360sList={threeSixties}
        set_360Object={set_360Object}
        onVideoSelect={handleVideoSelectionNotification}
        infoVideoMaker={infoVideoMaker}
      />

      {/* Texture Status Indicator */}
      <TextureStatusIndicator
        loadingQueue={loadingQueue}
        textureCache={textureCache}
        currentImageId={currentImage?._id}
        isVisible={!isLoading && !showHelp}
      />

      {/* Help Overlay */}
      {showHelp && (
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-40">
          <div className="bg-black/90 text-white p-8 rounded-lg max-w-md mx-4 border border-white/20">
            <h3 className="text-xl font-bold mb-4">360° Viewer Controls</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span>Look around:</span>
                <span className="text-gray-300">Click & drag / Touch & drag</span>
              </div>
              <div className="flex justify-between">
                <span>Previous image:</span>
                <span className="text-gray-300">← Arrow key / Left button</span>
              </div>
              <div className="flex justify-between">
                <span>Next image:</span>
                <span className="text-gray-300">→ Arrow key / Right button</span>
              </div>

              <div className="flex justify-between">
                <span>Select image:</span>
                <span className="text-gray-300">Click thumbnail</span>
              </div>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Got it!
            </button>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 75 }}
        className="w-full h-full"
        gl={{
          antialias: true,
          alpha: false,
          preserveDrawingBuffer: false
        }}
      >
        <Suspense fallback={null}>
          {/* Only render components when we have valid data to prevent glitches */}
          {currentImage && _360Object?._id && (
            <PanoramicSphere {...panoSphereProps} />
          )}
          {/* Render _360InfoMarkers only if we have a valid marker list and 360Object */}
          {_360Object?._id && _360Object?.markerList && Array.isArray(_360Object.markerList) && (
            <_360InfoMarkers {...infoMarkersProps} />
          )}
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

      {/* Loading Overlay for texture loading */}
      {isLoading && (
        <LoadingOverlay message="Loading textures..." />
      )}
    </div>
  );
}
