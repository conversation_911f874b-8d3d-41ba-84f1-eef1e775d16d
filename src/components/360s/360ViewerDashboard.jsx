'use client';

import { useState, useEffect, useRef, Suspense, useMemo, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { MdArrowBack, MdHelp, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphereDashbard';
import ThumbnailPanel from './ThumbnailPanel';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';
import TextureStatusIndicator from './TextureStatusIndicator';
import MarkerInputPanel from './MarkerInputPanel';
import _360InfoMarkers from './_360InfoMarkersDashboard';
import { useContextExperience } from '@/contexts/useContextExperience';
// import { sortedArray } from 'three/src/animation/AnimationUtils'; // This import seems unused and might cause issues if not a valid module

export default function ThreeSixtyViewer() {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  // Initialize _360Object with default structure to avoid undefined errors
  const [_360Object, set_360Object] = useState({
    cameraPosition: 0,
    _360Rotation: 0,
    markerList: [],
  });

  // Initialize info-video maker object to store selected video information
  const [infoVideoMaker, setInfoVideoMaker] = useState({
    _360Name: '',
    selectedVideoTitle: '',
    selectedVideoId: '',
    lastUpdated: null,
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [resetView, setResetView] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);

  const {experienceState,disptachExperience}=useContextExperience()

  const [showHelp, setShowHelp] = useState(false);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);

  // Notification state for video selection
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });

  // Derive currentImage from threeSixties array
  const currentImage = useMemo(() => {
    const image = threeSixties[currentImageIndex];
    return image;
  }, [threeSixties, currentImageIndex]);

  // console.log('ThreeSixtyViewer:',experienceState)


  // Function to update info-video maker object when video is selected
  const updateInfoVideoMaker = (videoTitle, videoId = '') => {
    const updatedData = {
      _360Name: currentImage?.name || '',
      selectedVideoTitle: videoTitle,
      selectedVideoId: videoId,
      lastUpdated: new Date().toISOString(),
    };

    setInfoVideoMaker(prev => ({
      ...prev,
      ...updatedData
    }));

    console.log('🎥 Info-video maker updated:', updatedData);

    // Show notification when video is selected
    if (videoTitle) {
      console.log(`✅ Video "${videoTitle}" selected for 360° image "${currentImage?.name}"`);
      setNotification({
        show: true,
        message: `Video "${videoTitle}" selected for ${currentImage?.name}`,
        type: 'success'
      });

      // Auto-hide notification after 3 seconds
      setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
    }
  };

  // Effect to update _360Object when currentImage changes with proper marker synchronization
  useEffect(() => {
    if (!currentImage) {
      // Clear state when no current image to prevent stale data
      set_360Object({
        _id: '',
        name: '',
        cameraPosition: 0,
        _360Rotation: 0,
        markerList: [],
      });

      // Clear info-video maker when no current image
      setInfoVideoMaker(prev => ({
        ...prev,
        _360Name: '',
        selectedVideoTitle: '',
        selectedVideoId: '',
        lastUpdated: null,
      }));
      return;
    }

    const new_360ObjectState = {
      _id: currentImage._id,
      name: currentImage.name, // Include name for proper state management
      cameraPosition: currentImage.cameraPosition || 0,
      _360Rotation: currentImage._360Rotation || 0,
      markerList: Array.isArray(currentImage.markerList) ? [...currentImage.markerList] : [], // Deep copy to prevent reference issues
    };

    // Always update state when currentImage changes to ensure marker synchronization
    set_360Object(new_360ObjectState);

    // Update info-video maker with current 360 name
    setInfoVideoMaker(prev => ({
      ...prev,
      _360Name: currentImage.name || '',
      lastUpdated: new Date().toISOString(),
    }));

    let timer;
    setResetView(true);
    timer = setTimeout(() => setResetView(false), 150); // Slightly longer delay for smoother transition

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentImage]);


  // Fetch 360° images from API
  useEffect(() => {
    fetchThreeSixties();
  }, []);

  // Add global refresh function for child components to trigger data refresh
  useEffect(() => {
    window.refreshDashboardData = () => {
      fetchThreeSixties(true); // Preserve current index when refreshing
    };

    // Suppress WebXR emulator extension warnings
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args.join(' ');
      if (message.includes('WebXR emulator extension') ||
          message.includes('overrides native WebXR API')) {
        return; // Suppress WebXR warnings
      }
      originalConsoleWarn.apply(console, args);
    };

    // Cleanup on unmount
    return () => {
      delete window.refreshDashboardData;
      console.warn = originalConsoleWarn; // Restore original console.warn
    };
  }, []);

  // --- Image change handler for dashboard ---
  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;

    setIsTransitioning(true);

    // Clear current marker state to prevent stale data during transition
    set_360Object(prev => ({
      ...prev,
      markerList: [] // Clear markers during transition
    }));

    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 300));

    setCurrentImageIndex(index);

    // Update info-video maker with the selected 360 image name
    const selectedImage = threeSixties[index];
    if (selectedImage) {
      updateInfoVideoMaker('', ''); // Clear video selection when changing 360 images
      console.log('360 Image selected:', selectedImage.name);
    }

    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 300));

    setIsTransitioning(false);
  };



  const fetchThreeSixties = async (preserveCurrentIndex = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/360s?sort=priority&order=asc&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        const sortedData = data.data.sort((b, a) => (a.priority || 0) - (b.priority || 0));
        setThreeSixties(sortedData);

        // Preserve current image index if refreshing, otherwise start from 0
        if (!preserveCurrentIndex) {
          setCurrentImageIndex(0);
        } else {
          // Ensure current index is still valid after refresh
          setCurrentImageIndex(prev => Math.min(prev, sortedData.length - 1));
        }

        initializeTextureLoading(sortedData);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    const sortedImages = [...images].sort((b, a) => (a.priority || 0) - (b.priority || 0));

    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index,
      status: 'pending'
    })));
  };

  const handleBack = () => {
    router.push('/admin/360s-manager/file-manager');
  };

  // Enhanced URL resolution for production compatibility
  const resolveImageUrl = useCallback((url) => {
    if (!url) return null;

    // If it's already a full URL (Firebase, CDN), return as-is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For local files, use our serving API in production
    if (process.env.NODE_ENV === 'production') {
      // Extract filename from path
      const filename = url.split('/').pop();
      return `/api/360s/serve/${filename}`;
    }

    // In development, use direct path
    return url.startsWith('/') ? url : `/${url}`;
  }, []);

  // Memoize the props object specifically for PanoramicSphere
  const panoSphereProps = useMemo(() => {
    const resolvedUrl = currentImage?.url ? resolveImageUrl(currentImage.url) : null;

    return {
      currentImage: currentImage,
      imageUrl: resolvedUrl,
      imageId: currentImage?._id,
      textureCache: textureCache,
      setTextureCache: setTextureCache,
      loadingQueue: loadingQueue,
      setLoadingQueue: setLoadingQueue,
      _360Object: _360Object, // Pass the actual _360Object state
      set_360Object: set_360Object,
      onTextureLoad: () => { /* Texture loaded successfully */ },
      resetView: resetView,
    };
  }, [
    currentImage,
    textureCache,
    setTextureCache,
    loadingQueue,
    setLoadingQueue,
    _360Object, // Dependency for _360Object state
    resetView,
    resolveImageUrl,
  ]);

  // Memoize the props object specifically for _360InfoMarkers with proper dependency tracking
  const infoMarkersProps = useMemo(() => {
    return {
      markerList: _360Object?.markerList || [],
      set_360Object: set_360Object,
      disptachExperience:disptachExperience,
      experienceState:experienceState,
      currentImageId: _360Object?._id, // Add currentImageId to force re-render on image change
    };
  }, [_360Object?.markerList, _360Object?._id, set_360Object]);


  if (isLoading || !currentImage) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* Top Controls */}
      <div className="absolute top-4 left-4 right-4 z-20 flex justify-between items-center">
        <button
          onClick={handleBack}
          className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
        >
          <MdArrowBack size={24} />
        </button>
        
        <div className="text-center text-white">
          <h1 className="text-xl font-bold">{currentImage?.name || 'Untitled'}</h1>
          <p className="text-sm text-gray-300">
            {currentImageIndex + 1} of {threeSixties.length}
          </p>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-lg transition-colors backdrop-blur-sm"
          >
            <MdHelp size={24} />
          </button>
        </div>
      </div>

      {/* Navigation Arrows */}
      {threeSixties.length > 1 && (
        <>
          <button
            onClick={() => handleImageChange(currentImageIndex - 1)}
            disabled={currentImageIndex === 0 || isTransitioning}
            className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === 0 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateBefore size={32} />
          </button>

          <button
            onClick={() => handleImageChange(currentImageIndex + 1)}
            disabled={currentImageIndex === threeSixties.length - 1 || isTransitioning}
            className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 text-white p-4 rounded-full transition-all backdrop-blur-sm ${
              currentImageIndex === threeSixties.length - 1 || isTransitioning
                ? 'opacity-30 cursor-not-allowed'
                : 'hover:scale-110'
            }`}
          >
            <MdNavigateNext size={32} />
          </button>
        </>
      )}

      {/* Thumbnail Panel */}
      <ThumbnailPanel
        images={threeSixties}
        currentIndex={currentImageIndex}
        onImageSelect={handleImageChange}
        isTransitioning={isTransitioning}
      />

      {/* Reset button */}
      <div onClick={()=>setResetView(!resetView)} className='absolute text-xs px-4 flex items-center cursor-pointer hover:bg-black/75 ease-linear left-20 text-white h-12 p-2 top-20 z-30 transition-all duration-300 capitalize bg-black/60 rounded-md'>reset view</div>

      {/* Debug Info - Remove in production */}
      {process.env.NODE_ENV === 'development' && (
        <div className='absolute top-20 left-4 bg-black/80 text-white p-2 rounded text-xs z-30 max-w-xs'>
          <div>Current Image ID: {currentImage?._id || 'none'}</div>
          <div>360Object ID: {_360Object?._id || 'none'}</div>
          <div>Markers Count: {_360Object?.markerList?.length || 0}</div>
          <div>Marker Names: {_360Object?.markerList?.map(m => m.name).join(', ') || 'none'}</div>
          <div className="mt-2 border-t border-white/20 pt-2">
            <div className="font-semibold text-green-400">📹 Info-Video Maker:</div>
            <div className={infoVideoMaker._360Name ? 'text-green-300' : 'text-gray-400'}>
              _360Name: {infoVideoMaker._360Name || 'none'}
            </div>
            <div className={infoVideoMaker.selectedVideoTitle ? 'text-green-300 font-semibold' : 'text-gray-400'}>
              Video Title: {infoVideoMaker.selectedVideoTitle || 'none'}
            </div>
            <div className={infoVideoMaker.selectedVideoId ? 'text-green-300' : 'text-gray-400'}>
              Video ID: {infoVideoMaker.selectedVideoId || 'none'}
            </div>
            <div className="text-blue-300">
              Last Updated: {infoVideoMaker.lastUpdated ? new Date(infoVideoMaker.lastUpdated).toLocaleTimeString() : 'never'}
            </div>
          </div>
        </div>
      )}

      {/* Marker Input Panel */}
      <MarkerInputPanel
        _360Object={_360Object}
        _360sList={threeSixties}
        set_360Object={set_360Object}
        onVideoSelect={updateInfoVideoMaker}
        infoVideoMaker={infoVideoMaker}
      />

      {/* Texture Status Indicator */}
      <TextureStatusIndicator
        loadingQueue={loadingQueue}
        textureCache={textureCache}
        currentImageId={currentImage?._id}
        isVisible={!isLoading && !showHelp}
      />

      {/* Help Overlay */}
      {showHelp && (
        <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-40">
          <div className="bg-black/90 text-white p-8 rounded-lg max-w-md mx-4 border border-white/20">
            <h3 className="text-xl font-bold mb-4">360° Viewer Controls</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span>Look around:</span>
                <span className="text-gray-300">Click & drag / Touch & drag</span>
              </div>
              <div className="flex justify-between">
                <span>Previous image:</span>
                <span className="text-gray-300">← Arrow key / Left button</span>
              </div>
              <div className="flex justify-between">
                <span>Next image:</span>
                <span className="text-gray-300">→ Arrow key / Right button</span>
              </div>

              <div className="flex justify-between">
                <span>Select image:</span>
                <span className="text-gray-300">Click thumbnail</span>
              </div>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Got it!
            </button>
          </div>
        </div>
      )}

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 75 }}
        className="w-full h-full"
        gl={{
          antialias: true,
          alpha: false,
          preserveDrawingBuffer: false
        }}
      >
        <Suspense fallback={null}>
          {/* Only render components when we have valid data to prevent glitches */}
          {currentImage && _360Object?._id && (
            <PanoramicSphere {...panoSphereProps} />
          )}
          {/* Render _360InfoMarkers only if we have a valid marker list and 360Object */}
          {_360Object?._id && _360Object?.markerList && Array.isArray(_360Object.markerList) && (
            <_360InfoMarkers {...infoMarkersProps} />
          )}
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

      {/* Loading Overlay for texture loading */}
      {isLoading && (
        <LoadingOverlay message="Loading textures..." />
      )}
    </div>
  );
}
